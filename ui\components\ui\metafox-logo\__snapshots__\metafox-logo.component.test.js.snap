// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MetaFoxLogo does not set icon height and width when unsetIconHeight is true 1`] = `
<div>
  <button
    class="mm-box app-header__logo-container mm-box--background-color-transparent"
    data-testid="app-header-logo"
  >
    <div />
    <img
      alt=""
      class="app-header__metafox-logo--icon"
      src="./images/logo/neonix_icon.png"
    />
  </button>
</div>
`;

exports[`MetaFoxLogo should match snapshot with img width and height default set to 42 1`] = `
<div>
  <button
    class="mm-box app-header__logo-container mm-box--background-color-transparent"
    data-testid="app-header-logo"
  >
    <div />
    <img
      alt=""
      class="app-header__metafox-logo--icon"
      height="42"
      src="./images/logo/neonix_icon.png"
      width="42"
    />
  </button>
</div>
`;
