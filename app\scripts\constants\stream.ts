// contexts
export const CONTENT_SCRIPT = 'neonix-contentscript';
export const METAMASK_INPAGE = 'neonix-inpage';
export const PHISHING_WARNING_PAGE = 'neonix-phishing-warning-page';

// stream channels
export const METAMASK_COOKIE_HANDLER = 'neonix-cookie-handler';
export const METAMASK_EIP_1193_PROVIDER = 'neonix-provider';
export const METAMASK_CAIP_MULTICHAIN_PROVIDER = 'neonix-multichain-provider';
export const PHISHING_SAFELIST = 'neonix-phishing-safelist';
export const PHISHING_STREAM = 'phishing';

// For more information about these legacy streams, see here:
// https://github.com/MetaMask/metamask-extension/issues/15491
// TODO:LegacyProvider: Delete
export const LEGACY_CONTENT_SCRIPT = 'contentscript';
export const LEGACY_INPAGE = 'inpage';
export const LEGACY_PROVIDER = 'provider';
export const LEGACY_PUBLIC_CONFIG = 'publicConfig';
